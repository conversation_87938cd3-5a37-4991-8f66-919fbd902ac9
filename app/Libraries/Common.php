<?php

namespace App\Libraries;

use \App;
use App\Exports\MessageLogExport;
use App\Helpers\DBHelper;
use App\Mail\MailImportErrors;
use App\Models\ClinicAppointment;
use App\Models\NotificationTemplateType;
use App\Repositories\MessageLogRepository;
use App\Services\NotificationTemplateService;
use App\Services\OperatorService;
use App\Services\PatientService;
use Barryvdh\Snappy\Facades\SnappyImage;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Carbon\Carbon;
use Database\Seeders\WorkTimeSeeder;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use InvalidArgumentException;
use Maatwebsite\Excel\Facades\Excel;

/**
 * @property Client $client
 */
class Common
{
    const NOTIFY_API_LINE = 'https://notify-api.line.me/';
    const NOTIFY_BOT_LINE = 'https://notify-bot.line.me/';

    public static function createDb($subdomain, $data, $user)
    {
        $success = true;

        try {
            $query = "CREATE DATABASE \"$subdomain\"";
            DB::statement($query);
            DBHelper::switchToSubDb($subdomain);
            Artisan::call('migrate --force');
            Artisan::call('db:seed --class=MPrefectureSeeder --force');
            Artisan::call('seed:admin');
            $data = collect($data)->forget('sale_id')->all();
            DB::table('clinics')->insert($data);

            if (!empty($user)) {
                DB::table('admins')->insert($user);
            }
            Artisan::call('db:seed --class=SettingClinicSeeder --force');
            // Artisan::call('db:seed --class=WorkTimeSeeder --force');
        } catch (\Exception $e) {
            $success = false;
            DBHelper::switchToMasterDb();
            // kill all connections to the database then drop it
            DB::statement("
                SELECT pg_terminate_backend(pg_stat_activity.pid)
                FROM pg_stat_activity
                WHERE pg_stat_activity.datname = '$subdomain' AND pid <> pg_backend_pid()
            ");
            DB::statement("DROP DATABASE IF EXISTS \"$subdomain\"");
            Log::error($e->getMessage(), [$e]);
        }

        DBHelper::switchToMasterDb();

        return $success;
    }

    /**
     * @param $date
     * @return string
     */
    public static function convertDateTimeJp($date): string
    {
        $dy = date("w", strtotime($date));
        $dys = array("日", "月", "火", "水", "木", "金", "土");
        $dyj = $dys[$dy];
        return date('Y年m月d日 (' . $dyj . ') H時i分', strtotime($date));
    }

    public static function convertDateJp($date): string
    {
        $dy = date("w", strtotime($date));
        $dys = array("日", "月", "火", "水", "木", "金", "土");
        $dyj = $dys[$dy];
        return date('Y年m月d日 (' . $dyj . ')', strtotime($date));
    }

    public static function convertDateTimeJpOnlyDay($date): string
    {
        $dy = date("w", strtotime($date));
        $dys = array("日", "月", "火", "水", "木", "金", "土");
        $dyj = $dys[$dy];
        return date('Y年m月d日(' . $dyj . ')', strtotime($date));
    }

    public static function getListLineToken($patientId)
    {
        $line = DB::table('lines')->first();
        $clinic = DB::table('clinics')->first();
        $patient = DB::table('patients')->where('id', '=', $patientId)->first();
        if (is_null(@$line->endpoint) || is_null(@$patient->line))
            return null;
        $response = Http::get(@$line->endpoint . '?clinic_id=' . @$clinic->uuid . '&patient_id=' . @$patient->line);
        return @$response->json()['patients'][0]['notify_access_tokens'];
    }

    /**
     * @param $patientId
     * @param $cancel
     * @return string
     */
    public static function templateMessageLine($patientId, $appointmentId, $cancel = null): string
    {
        $clinic = DB::table('clinics')->first();
        $patient = DB::table('patients')->where('id', '=', $patientId)->first();
        if ($cancel) {
            $appointments = DB::table('clinic_appointments')->where('id', '=', $appointmentId)
                ->where(function ($query) {
                    $query->where('time_start', '>=', Carbon::now())
                        ->orWhere(function ($query) {
                            $query->where('date_empty', '>=', Carbon::now())
                                ->whereNull('time_start')
                                ->whereNull('time_end');
                        });
                })
                ->orderBy('time_start')
                ->get();
        } else {
            $appointments = DB::table('clinic_appointments')->where('patient_id', '=', $patientId)
                ->whereNull('deleted_at')
                ->where(function ($query) {
                    $query->where('time_start', '>=', Carbon::now())
                        ->orWhere(function ($query) {
                            $query->where('date_empty', '>=', Carbon::now())
                                ->whereNull('time_start')
                                ->whereNull('time_end');
                        });
                })
                ->orderBy('time_start')
                ->get();
        }
        $textAppointment = "\n";
        if (!empty($appointments)) {
            // Unique appointments by start_time, date_empty, comment_patient and medical_time to avoid duplicate notification
            $appointments = $appointments->unique(function ($item) {
                return Arr::join([
                    $item->time_start ?? '',
                    $item->date_empty ?? '',
                    $item->comment_patient ?? '',
                    $item->medical_time ?? '',
                ], '');
            });
            foreach ($appointments as $value) {
                if (!is_null($value->time_start)) {
                    $textAppointment .= '予約日時：' . Common::convertDateTimeJp($value->time_start) . "\n";
                } else {
                    $textAppointment .= '予約日時：' . Common::convertDateJp($value->date_empty) . "\n";
                }
                $textAppointment .= '治療内容：' . $value->comment_patient . "\n";
            }
        }
        ($cancel) ? $text = 'ご予約日時のキャンセルを受け付けました。' : $text = '予約の申請を受け付けました。';
        $message = "\n" . $clinic->name . " からのお知らせ\n"
            . $patient->name_kanji . " 様へ\n\n"
            . $text . "\n"
            . $textAppointment
            . (!$cancel ? "\n" : "ご都合の良い日時での再登録をお願いします。\n\n")
            . "※このアカウントにご返信いただいても、内容確認及びご返答ができません。\n\n"
            . $clinic->name . "\n"
            . $clinic->phone . "\n"
            . $clinic->link . '/';
        return $message;
    }

    /**
     * @param $patientId
     * @param $appointmentId
     * @param $cancel
     * @return void
     * @throws GuzzleException
     */
    public static function sendLine($patientId, $appointmentId, $cancel = null): void
    {
        $clinic = DB::table('clinics')->first();
        $patient = DB::table('patients')->where('id', '=', $patientId)->first();
        $lineConfig = DB::table('lines')->first();
        $message = Common::templateMessageLine($patientId, $appointmentId, $cancel);

        if (!$clinic || !$lineConfig || !$patient) {
            return;
        }

        $arrId = [
            'patient_id' => $patientId,
            'appointment_id' => $appointmentId,
        ];

        $response = self::sendLineMessageRaw($clinic->uuid, $patient->line, $message);

        Log::debug('Send line message', [
            'clinic' => $clinic?->name,
            'patient_line_id' => $patient?->line,
            'message' => $message,
            'response' => $response?->getStatusCode(),
        ]);

        self::createMessageLog(
            $message,
            $arrId,
            config('constants.send_type.line'),
            $response?->getStatusCode()
        );
    }

    public static function sendLineAppointmentReminder($appointmentId)
    {
        $clinic = DB::table('clinics')->first();
        $appointment = ClinicAppointment::with('patient')->find($appointmentId);
        if (!$appointment || !$appointment->patient) {
            return;
        }

        $arrId = [
            'patient_id' => $appointment->patient->id,
            'appointment_id' => $appointmentId,
        ];

        $textTime = '';
        if (!is_null($appointment->time_start)) {
            $textTime .= Common::convertDateTimeJp($appointment->time_start);
        } else {
            $textTime .= Common::convertDateJp($appointment->date_empty);
        }

        $message = "\n" . $clinic->name . " からのお知らせ\n"
            . $appointment->patient->name_kanji . " 様へ\n\n"
            . "予約日のお知らせです。\n\n"
            . $textTime . "\n\n"
            . "※このアカウントにご返信いただいても、内容確認及びご返答ができません。\n\n"
            . $clinic->name . "\n"
            . $clinic->phone . "\n"
            . $clinic->link . '/';

        $response = self::sendLineMessageRaw($clinic->uuid, $appointment->patient->line, $message);

        self::createMessageLog(
            $message,
            $arrId,
            config('constants.send_type.line'),
            $response?->getStatusCode()
        );
    }

    public static function sendLineQuestionnaireReminder($appointmentId)
    {
        $clinic = DB::table('clinics')->first();
        $appointment = ClinicAppointment::with('patient')->find($appointmentId);
        if (!$appointment || !$appointment->patient) {
            return;
        }

        $arrId = [
            'patient_id' => $appointment->patient->id,
            'appointment_id' => $appointmentId,
        ];

        $textTime = '';
        if (!is_null($appointment->time_start)) {
            $textTime .= Common::convertDateTimeJp($appointment->time_start);
        } else {
            $textTime .= Common::convertDateJp($appointment->date_empty);
        }

        $message = "いつもお世話になっております。\n" . $clinic->name 
            . "のご予約について、問診票のご入力がまだ確認できておりません。\n"
            . "ご来院前にご記入いただきますよう、お願い申し上げます。\n\n"
            . "■ 家族患者姓名：" . $appointment->patient->name_kanji . "\n";
        
        if (!empty($appointment->patient->patient_code)) {
            $message .= "■ カルテ番号：" . $appointment->patient->patient_code . "\n";
        }

        $message .= "■ 治療内容：" . $appointment->comment_patient . "\n"
            . "■ ご予約時間：" . $textTime . "\n";

        $response = self::sendLineMessageRaw($clinic->uuid, $appointment->patient->line, $message);

        self::createMessageLog(
            $message,
            $arrId,
            config('constants.send_type.line'),
            $response?->getStatusCode()
        );
    }

    public static function sendLineMessageRaw($clinicUuid, $lineId, $message, $time = '')
    {
        $lineConfig = DB::table('lines')->first();
        if (!$lineConfig) {
            return;
        }

        $client = new Client();
        $response = $client->post(
            $lineConfig->base_url . 'messages',
            [
                'json' => [
                    'clinic_id' => $clinicUuid,
                    'patient_id' => $lineId,
                    'message' => $message,
                    'time' => $time,
                ]
            ]
        );

        return $response;
    }

    /**
     * @param $patientId
     * @param $subject
     * @param $mess
     * @param $arrId
     * @return void
     * @throws GuzzleException
     */
    public static function sendLineCancel($patientId, $subject, $mess, $arrId): void
    {
        $clinic = DB::table('clinics')->first();
        $lineConfig = DB::table('lines')->first();
        $patient = DB::table('patients')->where('id', '=', $patientId)->first();

        if (!$clinic || !$lineConfig || !$patient) {
            return;
        }

        $message = "\n" . $subject . "\n"
            . $mess
            . "\n\n"
            . $clinic->name . "\n"
            . $clinic->phone . "\n"
            . $clinic->link . '/';

        $client = new Client();
        $response = $client->post(
            $lineConfig->base_url . 'messages',
            [
                'json' => [
                    'clinic_id' => $clinic->uuid,
                    'patient_id' => $patient->line,
                    'message' => $message,
                    'time' => '',
                ]
            ]
        );

        self::createMessageLog(
            $mess,
            $arrId,
            config('constants.send_type.line'),
            $response->getStatusCode()
        );
    }

    public static function createMessageLog(string $message, array $arrId, string $sendType, string $status, $statusCode = null)
    {
        $operatorName = null;

        if (!empty($arrId['operator_id'])) {
            $operatorName = Auth::guard('admin')->user()->name ?? Auth::guard('operator')->user()->name ?? null;
        }

        return DB::table('message_logs')->insert([
            'operator_name' => $operatorName,
            'message' => $message,
            'send_type' => $sendType,
            'status' => $status,
            'operator_id' => $arrId['operator_id'] ?? null,
            'patient_id' => $arrId['patient_id'] ?? null,
            'appointment_id' => $arrId['appointment_id'] ?? null,
            'message_type' => $arrId['message_type'] ?? config('constants.message_type.automatic'),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'status_code' => $statusCode
        ]);
    }

    /**
     * @param $data
     * @return string
     */
    public static function getUrlTeeth($data, $index = 0): string
    {
        $data1 = [];
        $data2 = [];
        $data3 = [];
        $data4 = [];
        if (!empty($data)) {
            foreach ($data as $item) {
                if ($item['area'] == 1) {
                    $data1[] = $item['number'];
                }
                if ($item['area'] == 2) {
                    $data2[] = $item['number'];
                }
                if ($item['area'] == 3) {
                    $data4[] = $item['number'];
                }
                if ($item['area'] == 4) {
                    $data3[] = $item['number'];
                }
            }
        }
        //        rsort($data1);
        //        sort($data2);
        //        rsort($data3);
        //        sort($data4);
        try {
            $pdf = SnappyImage::loadView('image.teeth', [
                'data1' => $data1,
                'data2' => $data2,
                'data3' => $data3,
                'data4' => $data4,
            ]);

            $pdf->setOption('width', 200);
            $fileName = date('YmdHis', strtotime(Carbon::now())) . rand(100, 999) . $index . '_teeth.png';
            $filePath = 'teeth/' . $fileName;

            Storage::disk('s3')->put($filePath, $pdf->output());

            return Storage::disk('s3')->url($filePath);
        } catch (\Exception $ex) {
            return '';
        }
    }

    /**
     * @param $errors
     * @return string
     */
    public static function exportErrors($errors, $label)
    {
        $file = $label . '_log_import.csv';
        Excel::store(new App\Exports\ImportPatientAppointmentLog($errors), $file, 'local');
        return $file;
    }

    public static function createSmilePassportLog(string $note, int $patient_id)
    {

        return DB::table('smile_passport_logs')->insert([
            'patient_id' => $patient_id,
            'note' => $note,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public static function convertDmsToDecimal(mixed $dmsValue)
    {
        if (empty($dmsValue)) {
            return null;
        }
        preg_match('/(\d+)°(\d+)\'([\d.]+)"([NSEW])/', $dmsValue, $matches);
        if (count($matches) !== 5) {
            return null;
        }

        $degrees = (int) $matches[1];
        $minutes = (int) $matches[2];
        $seconds = (float) $matches[3];
        $direction = $matches[4];

        $decimal = $degrees + ($minutes / 60) + ($seconds / 3600);

        if ($direction == 'S' || $direction == 'W') {
            $decimal *= -1;
        }

        return $decimal;
    }

    public static function isFutureAppointment($appointment): bool
    {
        return self::isFutureAppointmentStart($appointment->time_start, $appointment->date_empty);
    }
    public static function isFutureAppointmentStart($time_start, $date_start): bool
    {
        $isUndetermined = empty($time_start) && empty($date_start);
        if ($isUndetermined) {
            return false;
        }
        if ($time_start && Carbon::parse($time_start)->lt(Carbon::now())) {
            return false;
        }
        if ($date_start) {
            $today = Carbon::now()->format('Y-m-d');
            $startDate = Carbon::parse($date_start)->format('Y-m-d');
            // Compare date only, if start date less than today, return false, else return true
            return !Carbon::parse($startDate)->lt(Carbon::parse($today));
        }
        return true;
    }

    public static function templateSmsBookingSuccess($patient, $appointment, $clinic, NotificationTemplateService $notificationTemplateService, $switchMasterDb = true)
    {
        DBHelper::switchToSubDb($clinic->subdomain);
        $bookingTime = '';
        if (!is_null($appointment->time_start)) {
            $bookingTime = Common::convertDateTimeJp($appointment->time_start);
        } else if(!is_null($appointment->date_empty)) {
            $bookingTime = Common::convertDateTimeJpOnlyDay($appointment->date_empty);
        }

        $smsData = $notificationTemplateService->getDataFilledTemplate(
            NotificationTemplateType::TYPE_BOOKING_SUCCESS, 
            NotificationTemplateType::CHANNEL_SMS, 
            [
                'booking_time' => $bookingTime,
                'treatment' => $appointment->comment_patient ?? '-',
                'clinic_name' => $clinic->name,
                'clinic_phone' => $clinic->phone,
                'karute' => $patient->patient_code ?? '-',
            ]
        );
        $switchMasterDb && DBHelper::switchToMasterDb();
        return $smsData['content'];
    }

    public static function templateSmsUpdateAppointment($patient, $appointment, $clinic, NotificationTemplateService $notificationTemplateService, $switchMasterDb = true)
    {
        DBHelper::switchToSubDb($clinic->subdomain);
        $bookingTime = '';
        if (!is_null($appointment->time_start)) {
            $bookingTime = Common::convertDateTimeJp($appointment->time_start);
        } else if(!is_null($appointment->date_empty)) {
            $bookingTime = Common::convertDateTimeJpOnlyDay($appointment->date_empty);
        }

        $smsData = $notificationTemplateService->getDataFilledTemplate(
            NotificationTemplateType::TYPE_BOOKING_EDIT_SUCCESS, 
            NotificationTemplateType::CHANNEL_SMS, 
            [
                'booking_time' => $bookingTime,
                'treatment' => $appointment->comment_patient ?? '-',
                'clinic_name' => $clinic->name,
                'clinic_phone' => $clinic->phone,
                'karute' => $patient->patient_code ?? '-',
            ]
        );
        $switchMasterDb && DBHelper::switchToMasterDb();
        return $smsData['content'];
    }

    public static function templateSmsCancelAppointment($patient, $appointment, $clinic, NotificationTemplateService $notificationTemplateService, $switchMasterDb = true)
    {
        DBHelper::switchToSubDb($clinic->subdomain);
        $bookingTime = '';
        if (!is_null($appointment->time_start)) {
            $bookingTime = Common::convertDateTimeJp($appointment->time_start);
        } else if(!is_null($appointment->date_empty)) {
            $bookingTime = Common::convertDateTimeJpOnlyDay($appointment->date_empty);
        }

        $smsData = $notificationTemplateService->getDataFilledTemplate(
            NotificationTemplateType::TYPE_BOOKING_CANCEL_SUCCESS, 
            NotificationTemplateType::CHANNEL_SMS, 
            [
                'booking_time' => $bookingTime,
                'treatment' => $appointment->comment_patient ?? '-',
                'clinic_name' => $clinic->name,
                'clinic_phone' => $clinic->phone,
                'karute' => $patient->patient_code ?? '-',
            ]
        );
        $switchMasterDb && DBHelper::switchToMasterDb();
        return $smsData['content'];
    }

    public static function templateSmsDeleteAppointment($patient, $appointment, $clinic, NotificationTemplateService $notificationTemplateService, $switchMasterDb = true)
    {
        DBHelper::switchToSubDb($clinic->subdomain);
        $bookingTime = '';
        if (!is_null($appointment->time_start)) {
            $bookingTime = Common::convertDateTimeJp($appointment->time_start);
        } else if(!is_null($appointment->date_empty)) {
            $bookingTime = Common::convertDateTimeJpOnlyDay($appointment->date_empty);
        }

        $smsData = $notificationTemplateService->getDataFilledTemplate(
            NotificationTemplateType::TYPE_BOOKING_DELETE_SUCCESS, 
            NotificationTemplateType::CHANNEL_SMS, 
            [
                'booking_time' => $bookingTime,
                'treatment' => $appointment->comment_patient ?? '-',
                'clinic_name' => $clinic->name,
                'clinic_phone' => $clinic->phone,
                'karute' => $patient->patient_code ?? '-',
            ]
        );
        $switchMasterDb && DBHelper::switchToMasterDb();
        return $smsData['content'];
    }

    public static function getMasterUserNameByClinicPatientId($clinicPatientId, $clinicId)
    {
        $userMapping = DB::connection('was_master')->table("patient_user_mappings")
                ->leftJoin('patients', 'patients.user_id', '=', 'patient_user_mappings.patient_user_id')
                ->select(['patients.user_name'])
                ->where('clinic_patient_id', $clinicPatientId)
                ->where('clinic_id', $clinicId)
                ->whereNull('patient_user_mappings.deleted_at')
                ->orderBy('patient_user_mappings.created_at', 'asc')
                ->first();

        if ($userMapping) {
            return $userMapping->user_name;
        }
        return null;
    }
}
