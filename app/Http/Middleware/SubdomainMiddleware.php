<?php

namespace App\Http\Middleware;

use App\Libraries\Common;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

class SubdomainMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $subdomain = explode('://', $request->getHost())[0];
        if ($subdomain == env('DOMAIN')) {
            // Because some logic is used by both master and subdomain => need to check if it is subdomain or master
            $request->attributes->add(['subdomain' => null]);
            return $next($request);
        }
        $subdomain = explode('.', $subdomain)[0];
        $request->attributes->add(['subdomain' => $subdomain]);
        $clinic = DB::table('clinics')->where('subdomain', $subdomain)->whereNull('deleted_at')->first();
        if(is_null($clinic)) {
            $response = [
                'success' => false,
                'message' => 'Not found',
                'data' => null,
            ];
            return response()->json($response, 404);
        }

        if (is_null($clinic->end_time)) {
            if (@$clinic->subdomain == $subdomain && $request->getHost() != env('DOMAIN')) {
                Config::set('database.connections.pgsql.database', $clinic->subdomain);
                DB::purge('pgsql');
                return $next($request);
            }
        } else if (@$clinic->start_time <= Carbon::now() && Carbon::parse(@$clinic->end_time)->endOfDay() >= Carbon::now()) {
            if (@$clinic->subdomain == $subdomain && $request->getHost() != env('DOMAIN')) {
                Config::set('database.connections.pgsql.database', $clinic->subdomain);
                DB::purge('pgsql');
            }
            else
            {
                $response = [
                    'success' => false,
                    'message' => 'Not found',
                    'data' => null,
                ];
                return  response()->json($response, 404);
            }
        }
        else {
            $response = [
                'success' => false,
                'message' => 'Not found',
                'data' => null,
            ];
            return  response()->json($response, 404);
        }
        return $next($request);
    }
}
